<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Storage Image Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-image {
            max-width: 300px;
            max-height: 200px;
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Firebase Storage Image Access Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Direct Image Tag</h2>
        <p>Testing with regular HTML img tag:</p>
        <img id="test-img-1" class="test-image" alt="Test Image 1">
        <div id="result-1" class="log"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: JavaScript Fetch</h2>
        <p>Testing with JavaScript fetch API:</p>
        <div id="result-2" class="log"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Image with CORS</h2>
        <p>Testing with crossOrigin attribute:</p>
        <img id="test-img-3" class="test-image" alt="Test Image 3" crossorigin="anonymous">
        <div id="result-3" class="log"></div>
    </div>

    <script>
        // Test URLs from the Flutter app logs
        const testUrls = [
            'https://firebasestorage.googleapis.com/v0/b/money-mouthy.firebasestorage.app/o/user_uploads%2FKFmrsrrgt2NiDUAwLhaILWa70ji2%2Fprofile_1751868006940.jpg?alt=media&token=6e457a4e-aa24-4c3f-a319-c9e2adcc7aed',
            'https://firebasestorage.googleapis.com/v0/b/money-mouthy.firebasestorage.app/o/post_images%2FKFmrsrrgt2NiDUAwLhaILWa70ji2%2F1751915991104_0.jpg?alt=media&token=fa1b6ecb-2f98-4937-9d15-9d1393a7d1c6'
        ];

        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = isError ? 'error' : 'success';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        }

        // Test 1: Direct image tag
        function test1() {
            const img = document.getElementById('test-img-1');
            const url = testUrls[0];
            
            img.onload = () => {
                log('result-1', `✅ Image loaded successfully: ${url}`);
            };
            
            img.onerror = (error) => {
                log('result-1', `❌ Image failed to load: ${error.message || 'Unknown error'}`, true);
            };
            
            log('result-1', `🔄 Loading image: ${url}`);
            img.src = url;
        }

        // Test 2: JavaScript fetch
        async function test2() {
            const url = testUrls[0];
            log('result-2', `🔄 Fetching image: ${url}`);
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    log('result-2', `✅ Fetch successful: ${response.status} ${response.statusText}`);
                    log('result-2', `Content-Type: ${response.headers.get('content-type')}`);
                    log('result-2', `Content-Length: ${response.headers.get('content-length')}`);
                } else {
                    log('result-2', `❌ Fetch failed: ${response.status} ${response.statusText}`, true);
                }
            } catch (error) {
                log('result-2', `❌ Fetch error: ${error.message}`, true);
            }
        }

        // Test 3: Image with CORS
        function test3() {
            const img = document.getElementById('test-img-3');
            const url = testUrls[1];
            
            img.onload = () => {
                log('result-3', `✅ CORS image loaded successfully: ${url}`);
            };
            
            img.onerror = (error) => {
                log('result-3', `❌ CORS image failed to load: ${error.message || 'Unknown error'}`, true);
            };
            
            log('result-3', `🔄 Loading CORS image: ${url}`);
            img.src = url;
        }

        // Run tests
        window.onload = () => {
            test1();
            test2();
            test3();
        };
    </script>
</body>
</html>
