# Web Image Loading Debug Guide

## Issue Summary
Images are not showing on Flutter web but work on mobile and when opened directly in browser.

## Changes Made

### 1. Created WebSafeImage Widget
- **File**: `lib/widgets/web_safe_image.dart`
- **Purpose**: Handles web-specific image loading issues
- **Features**:
  - Web-specific preloading with proper CORS headers
  - Firebase Storage URL processing (adds `alt=media` parameter)
  - Better error handling and debugging
  - Fallback loading and error widgets

### 2. Updated Post Cards
- **File**: `lib/widgets/post_card.dart`
- **Changes**: Replaced all `Image.network` with `FirebaseStorageImage`
- **Benefits**: Better error handling and web compatibility

### 3. Updated Image Gallery
- **File**: `lib/screens/image_gallery_screen.dart`
- **Changes**: Replaced `Image.network` with `FirebaseStorageImage`

### 4. Updated Profile Screen
- **File**: `lib/screens/profile_screen.dart`
- **Changes**: Replaced `NetworkImage` in CircleAvatar with custom `FirebaseStorageImage` widget

### 5. Web Configuration
- **File**: `web/index.html`
- **Changes**: Added CORS and security headers

### 6. CORS Configuration
- **Files**: `cors.json`, `cors-production.json`, `apply-cors.sh`
- **Purpose**: Configure Firebase Storage CORS settings

## Debugging Steps

### 1. Check Browser Console
Open browser developer tools (F12) and check for:
- CORS errors
- Network request failures
- JavaScript errors related to image loading

### 2. Test Firebase Storage CORS
Run the CORS configuration script:
```bash
./apply-cors.sh
```

### 3. Check Firebase Storage Rules
Verify that `storage.rules` allows read access:
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write;
    }
  }
}
```

### 4. Test Image URLs Directly
1. Copy an image URL from the app
2. Open it directly in browser
3. Check if it loads with `?alt=media` parameter

### 5. Check Network Tab
In browser dev tools:
1. Go to Network tab
2. Filter by "Img" or "XHR"
3. Look for failed image requests
4. Check response headers and status codes

## Common Issues and Solutions

### Issue 1: CORS Errors
**Symptoms**: Console shows CORS policy errors
**Solution**: Apply CORS configuration using `apply-cors.sh`

### Issue 2: Missing alt=media Parameter
**Symptoms**: Firebase Storage URLs return HTML instead of image
**Solution**: Our `FirebaseStorageImage` widget automatically adds this parameter

### Issue 3: Authentication Issues
**Symptoms**: 403 Forbidden errors
**Solution**: Check Firebase Storage rules and ensure proper authentication

### Issue 4: Network Policy Issues
**Symptoms**: Images blocked by browser security
**Solution**: Check web/index.html meta tags and CORS headers

## Testing Commands

### Test CORS Configuration
```bash
# Check current CORS settings
gsutil cors get gs://money-mouthy.firebasestorage.app

# Apply development CORS (allows all origins)
gsutil cors set cors.json gs://money-mouthy.firebasestorage.app

# Apply production CORS (specific origins only)
gsutil cors set cors-production.json gs://money-mouthy.firebasestorage.app
```

### Test Firebase Storage Rules
```bash
# Deploy storage rules
firebase deploy --only storage
```

## Expected Behavior After Fix
1. Images should load properly on Flutter web
2. Loading indicators should show while images are loading
3. Error widgets should display if images fail to load
4. Console should show detailed error messages for debugging

## Additional Notes
- The `WebSafeImage` widget includes comprehensive error logging
- All image loading now goes through the same consistent pipeline
- Firebase Storage URLs are automatically processed for web compatibility
- CORS headers are properly configured for cross-origin requests
