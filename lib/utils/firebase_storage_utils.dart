import 'package:flutter/foundation.dart';

class FirebaseStorageUtils {
  /// Converts a Firebase Storage URL to a web-compatible public URL
  static String getWebCompatibleUrl(String originalUrl) {
    if (!kIsWeb) {
      return originalUrl;
    }

    // If it's already a proper Firebase Storage URL with token, try to make it more web-compatible
    if (originalUrl.contains('firebasestorage.googleapis.com')) {
      try {
        final uri = Uri.parse(originalUrl);

        // For Firebase Storage URLs, we need to ensure proper CORS handling
        // Try using the public access pattern
        if (uri.pathSegments.length >= 4 &&
            uri.pathSegments[0] == 'v0' &&
            uri.pathSegments[1] == 'b') {
          final bucket = uri.pathSegments[2];
          final objectPath = uri.pathSegments.skip(4).join('/');

          // Create a public access URL
          final publicUrl =
              'https://firebasestorage.googleapis.com/v0/b/$bucket/o/${Uri.encodeComponent(objectPath)}?alt=media';

          // If there was a token in the original URL, preserve it
          if (uri.queryParameters.containsKey('token')) {
            return '$publicUrl&token=${uri.queryParameters['token']}';
          }

          return publicUrl;
        }

        // Fallback: ensure alt=media parameter is present
        final queryParams = Map<String, String>.from(uri.queryParameters);
        if (!queryParams.containsKey('alt')) {
          queryParams['alt'] = 'media';
        }

        // Rebuild the URL with proper parameters
        final newUri = uri.replace(queryParameters: queryParams);
        return newUri.toString();
      } catch (e) {
        debugPrint('❌ Error processing Firebase Storage URL: $e');
        return originalUrl;
      }
    }

    return originalUrl;
  }

  /// Converts a Firebase Storage gs:// URL to a web-compatible HTTP URL
  static String convertGsUrlToHttp(String gsUrl) {
    if (!gsUrl.startsWith('gs://')) {
      return gsUrl;
    }

    // Extract bucket and path from gs:// URL
    final withoutProtocol = gsUrl.substring(5); // Remove 'gs://'
    final parts = withoutProtocol.split('/');

    if (parts.isEmpty) {
      return gsUrl;
    }

    final bucket = parts[0];
    final path = parts.skip(1).join('/');

    // Convert to HTTP URL with proper encoding
    final encodedPath = Uri.encodeComponent(path);
    return 'https://firebasestorage.googleapis.com/v0/b/$bucket/o/$encodedPath?alt=media';
  }

  /// Debug function to log URL transformations
  static void debugUrl(String originalUrl, String transformedUrl) {
    if (kDebugMode) {
      print('🔗 URL Transform:');
      print('   Original: $originalUrl');
      print('   Transformed: $transformedUrl');
      print('   Same: ${originalUrl == transformedUrl}');
    }
  }
}
