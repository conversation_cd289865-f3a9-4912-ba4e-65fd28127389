import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:html' as html show ImageElement;

class WebSafeImage extends StatefulWidget {
  final String imageUrl;
  final BoxFit? fit;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final double? width;
  final double? height;
  final Widget Function(BuildContext, Widget, ImageChunkEvent?)? loadingBuilder;
  final Widget Function(BuildContext, Object, StackTrace?)? errorBuilder;

  const WebSafeImage({
    super.key,
    required this.imageUrl,
    this.fit,
    this.loadingWidget,
    this.errorWidget,
    this.width,
    this.height,
    this.loadingBuilder,
    this.errorBuilder,
  });

  @override
  State<WebSafeImage> createState() => _WebSafeImageState();
}

class _WebSafeImageState extends State<WebSafeImage> {
  bool _hasError = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _preloadImageForWeb();
    }
  }

  void _preloadImageForWeb() {
    if (!kIsWeb) return;

    try {
      final img = html.ImageElement();
      img.crossOrigin = 'anonymous';

      img.onLoad.listen((_) {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _hasError = false;
          });
        }
      });

      img.onError.listen((_) {
        debugPrint('❌ Web image load error for: ${widget.imageUrl}');
        if (mounted) {
          setState(() {
            _isLoading = false;
            _hasError = true;
          });
        }
      });

      // For Firebase Storage URLs, ensure proper parameters
      String processedUrl = widget.imageUrl;
      if (widget.imageUrl.contains('firebasestorage.googleapis.com')) {
        if (!widget.imageUrl.contains('alt=media')) {
          processedUrl =
              widget.imageUrl.contains('?')
                  ? '${widget.imageUrl}&alt=media'
                  : '${widget.imageUrl}?alt=media';
        }
      }

      img.src = processedUrl;
    } catch (e) {
      debugPrint('❌ Error preloading web image: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (kIsWeb && _hasError) {
      return widget.errorWidget ??
          widget.errorBuilder?.call(context, 'Image load failed', null) ??
          _buildDefaultErrorWidget();
    }

    if (kIsWeb && _isLoading) {
      return widget.loadingWidget ?? _buildDefaultLoadingWidget();
    }

    return Image.network(
      widget.imageUrl,
      fit: widget.fit,
      width: widget.width,
      height: widget.height,
      loadingBuilder:
          widget.loadingBuilder ??
          (context, child, loadingProgress) {
            if (loadingProgress == null) {
              return child;
            }
            return widget.loadingWidget ?? _buildDefaultLoadingWidget();
          },
      errorBuilder:
          widget.errorBuilder ??
          (context, error, stackTrace) {
            debugPrint('❌ Image.network error for ${widget.imageUrl}: $error');
            return widget.errorWidget ?? _buildDefaultErrorWidget();
          },
      headers:
          kIsWeb
              ? {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers':
                    'Origin, Content-Type, Accept, Authorization, X-Requested-With',
              }
              : null,
    );
  }

  Widget _buildDefaultLoadingWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.grey[100],
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4285F4)),
        ),
      ),
    );
  }

  Widget _buildDefaultErrorWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.grey[200],
      child: const Icon(Icons.image_not_supported, color: Colors.grey),
    );
  }
}

// Helper widget for Firebase Storage images specifically
class FirebaseStorageImage extends StatelessWidget {
  final String imageUrl;
  final BoxFit? fit;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final double? width;
  final double? height;

  const FirebaseStorageImage({
    super.key,
    required this.imageUrl,
    this.fit,
    this.loadingWidget,
    this.errorWidget,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    // For Firebase Storage URLs, add token parameter if missing
    String processedUrl = imageUrl;

    if (kIsWeb && imageUrl.contains('firebasestorage.googleapis.com')) {
      // Add alt=media parameter if missing for Firebase Storage
      if (!imageUrl.contains('alt=media')) {
        processedUrl =
            imageUrl.contains('?')
                ? '$imageUrl&alt=media'
                : '$imageUrl?alt=media';
      }
    }

    return WebSafeImage(
      imageUrl: processedUrl,
      fit: fit,
      loadingWidget: loadingWidget,
      errorWidget: errorWidget,
      width: width,
      height: height,
    );
  }
}
