#!/usr/bin/env node

/**
 * <PERSON>ript to configure CORS for Firebase Storage
 * This script uses the Google Cloud Storage client library to set CORS configuration
 */

const { Storage } = require('@google-cloud/storage');
const path = require('path');

// Initialize Google Cloud Storage
const storage = new Storage({
  projectId: 'money-mouthy',
  keyFilename: path.join(__dirname, 'service-account-key.json'), // You'll need to download this
});

const bucketName = 'money-mouthy.firebasestorage.app';

const corsConfiguration = [
  {
    origin: ['*'], // Allow all origins for development
    method: ['GET', 'HEAD', 'PUT', 'POST', 'DELETE'],
    responseHeader: ['Content-Type', 'Access-Control-Allow-Origin'],
    maxAgeSeconds: 3600,
  },
];

async function configureCORS() {
  try {
    console.log('🔧 Configuring CORS for Firebase Storage...');
    
    const bucket = storage.bucket(bucketName);
    
    // Set CORS configuration
    await bucket.setCorsConfiguration(corsConfiguration);
    
    console.log('✅ CORS configuration applied successfully!');
    
    // Verify the configuration
    const [metadata] = await bucket.getMetadata();
    console.log('🔍 Current CORS configuration:');
    console.log(JSON.stringify(metadata.cors, null, 2));
    
  } catch (error) {
    console.error('❌ Error configuring CORS:', error.message);
    
    if (error.message.includes('service-account-key.json')) {
      console.log('\n📋 To fix this error:');
      console.log('1. Go to Firebase Console > Project Settings > Service Accounts');
      console.log('2. Click "Generate new private key"');
      console.log('3. Save the file as "service-account-key.json" in this directory');
      console.log('4. Run this script again');
    }
  }
}

// Alternative method using environment variables
async function configureCORSWithEnv() {
  try {
    console.log('🔧 Configuring CORS using environment credentials...');
    
    // This will use GOOGLE_APPLICATION_CREDENTIALS environment variable
    const storage = new Storage({
      projectId: 'money-mouthy',
    });
    
    const bucket = storage.bucket(bucketName);
    await bucket.setCorsConfiguration(corsConfiguration);
    
    console.log('✅ CORS configuration applied successfully!');
    
  } catch (error) {
    console.error('❌ Error configuring CORS:', error.message);
  }
}

// Check if we have credentials and run appropriate method
if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
  configureCORSWithEnv();
} else {
  configureCORS();
}
